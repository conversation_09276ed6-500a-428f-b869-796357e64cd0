import React, {
  useState,
  useRef,
  useEffect,
  useContext,
  useCallback,
} from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import {
  CardElement,
  Elements,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import SignaturePad from "react-signature-pad-wrapper";
import MkdSDK from "Utils/MkdSDK";
import { showToast } from "Context/Global";
import {
  uploadFilesAPI,
  uploadS3FilesAPI,
} from "Src/services/workOrderService";
import { ClipLoader } from "react-spinners";
import { GlobalContext } from "Src/globalContext";
import ServiceAgreementPDF from "./ServiceAgreementPDF";
import html2canvas from "html2canvas";

export const STEPS = {
  INVOICE_LIST_QUOTE: 1,
  TERMS_AGREEMENT: 2,
  PRIVACY_POLICY: 3,
  SIGN_REVIEW_SERVICE: 4,
  CLIENT_DETAILS: 5,
  PAYMENT: 6,
};

const schema = yup.object().shape({
  firstName: yup.string().required("First name is required"),
  lastName: yup.string().required("Last name is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  phone: yup.string().required("Phone number is required"),
  position: yup.string().required("Position is required"),
  program: yup.string().required("Program is required"),
  notes: yup.string(),
});

const InvoiceMultiStep = ({
  invoiceData,
  onSubmit,
  termsContent,
  serviceAgreementContent,
  token,
  clientData,
  step2Info,
  setStep2Info,
  companyInfo,
  legalDocuments,
}) => {
  console.log(companyInfo);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  console.log("Step 2 Info:", step2Info);
  console.log("Invoice Data:", invoiceData);
  console.log("Client Data:", clientData);
  const [currentStep, setCurrentStep] = useState(STEPS.INVOICE_LIST_QUOTE);
  const [signature, setSignature] = useState(step2Info?.signature);
  console.log("Signature:", signature);
  console.log("logo", companyInfo?.company_logo);
  const [initial, setInitial] = useState(
    step2Info?.initial || invoiceData?.invoice?.initials || ""
  );
  const [feedback, setFeedback] = useState(
    step2Info?.feedback_notes || invoiceData?.invoice?.feedback_notes || ""
  );
  const [paymentType, setPaymentType] = useState("deposit");
  const [paymentAmount, setPaymentAmount] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [paymentMethodId, setPaymentMethodId] = useState(null);
  const [paymentError, setPaymentError] = useState(null);
  const [paymentNotes, setPaymentNotes] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("stripe");
  const signaturePadRef = useRef(null);
  const stripe = useStripe();
  const elements = useElements();
  const [generatingPdf, setGeneratingPdf] = useState(false);
  const [termsAgreed, setTermsAgreed] = useState(false);
  const [privacyAgreed, setPrivacyAgreed] = useState(false);
  const [generatedPdfUrl, setGeneratedPdfUrl] = useState(null);
  console.log(selectedItems);
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: invoiceData?.invoice?.client_name?.split(" ")[0] || "",
      lastName: invoiceData?.invoice?.client_name?.split(" ")[1] || "",
      email: invoiceData?.invoice?.client_email || clientData?.email || "",
      programName: invoiceData?.invoice?.program || clientData?.program || "",
      phone: invoiceData?.invoice?.created_by_phone || clientData?.phone || "",
      position: clientData?.position || "",
      program: clientData?.program || "",
      notes: invoiceData?.invoice?.notes || "",
    },
  });

  // Update form values when clientData changes
  useEffect(() => {
    if (clientData) {
      setValue("email", clientData.email || "");
      setValue("phone", clientData.phone || "");
      setValue("position", clientData.position || "");
      setValue("program", clientData.program || "");
      const [firstName, ...lastNameParts] = (clientData.name || "").split(" ");
      setValue("firstName", firstName || "");
      setValue("lastName", lastNameParts.join(" ") || "");
    }
  }, [clientData, setValue]);

  // Update signature pad with existing signature if available
  useEffect(() => {
    if (signaturePadRef.current && invoiceData?.invoice?.signature) {
      const img = new Image();
      img.onload = () => {
        const canvas = signaturePadRef.current._canvas;
        const ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      };
      img.src = `https://equalityrecords.s3.amazonaws.com/${invoiceData.invoice.signature}`;
    }
  }, [invoiceData?.invoice?.signature]);

  // We're now setting selected items directly in the payment option handlers

  // Calculate total amount already paid
  const calculateTotalPaid = () => {
    if (!invoiceData?.payments) return 0;

    const successfulPayments = invoiceData.payments.filter(
      (p) => p.status === "succeeded"
    );

    return successfulPayments.reduce(
      (sum, payment) => sum + parseFloat(payment.amount || 0),
      0
    );
  };

  // Calculate remaining amount to be paid
  const calculateRemainingAmount = useCallback(() => {
    const totalInvoiceAmount = parseFloat(invoiceData?.invoice?.total || 0);
    // Use payment_amount from invoice data (already in dollars, not cents)
    const totalPaid = parseFloat(invoiceData?.invoice?.payment_amount || 0);
    return Math.max(totalInvoiceAmount - totalPaid, 0);
  }, [invoiceData]);

  // Calculate remaining deposit amount
  const calculateRemainingDeposit = useCallback(() => {
    // Deposit amount is already in dollars
    const depositAmount = parseFloat(invoiceData?.invoice?.deposit_amount || 0);
    // Use payment_amount from invoice data (already in dollars, not cents)
    const totalPaid = parseFloat(invoiceData?.invoice?.payment_amount || 0);
    return Math.max(depositAmount - totalPaid, 0);
  }, [invoiceData]);

  // Initialize payment amount when invoice data is loaded
  useEffect(() => {
    if (invoiceData) {
      const remainingDeposit = calculateRemainingDeposit();
      const remainingTotal = calculateRemainingAmount();

      // If deposit is fully paid but there's still a remaining balance
      if (remainingDeposit <= 0 && remainingTotal > 0) {
        setPaymentType("full");
        setPaymentAmount(remainingTotal);
      } else {
        setPaymentType("deposit");
        setPaymentAmount(remainingDeposit);
      }

      // Select all items automatically
      if (invoiceData.items && invoiceData.items.length > 0) {
        setSelectedItems(invoiceData.items.map((item) => item.id));
      }

      // Initialize generated PDF URL if it exists
      if (invoiceData?.invoice?.attachment_url || step2Info?.attachment_url) {
        setGeneratedPdfUrl(
          invoiceData?.invoice?.attachment_url || step2Info?.attachment_url
        );
      }
    }
  }, [
    invoiceData,
    step2Info,
    calculateRemainingDeposit,
    calculateRemainingAmount,
  ]);

  // Update Payment Amount Display when payment type changes
  useEffect(() => {
    if (paymentType === "deposit") {
      setPaymentAmount(calculateRemainingDeposit());
    } else {
      setPaymentAmount(calculateRemainingAmount());
    }
  }, [
    paymentType,
    invoiceData,
    calculateRemainingDeposit,
    calculateRemainingAmount,
  ]);

  const updateStepCompletion = async (step, data) => {
    try {
      const sdk = new MkdSDK();

      if (step === STEPS.INVOICE_LIST_QUOTE) {
        // For invoice list/quote step, we need to send feedback notes
        const payload = {
          feedback_notes: data.feedback,
        };

        const response = await sdk.callRawAPI(
          `/v3/api/custom/equality_record/subscription/public/invoice/${invoiceData?.invoice?.id}/${token}`,
          payload,
          "PUT"
        );

        if (response.error) {
          throw new Error(
            response.message || "Failed to update feedback notes"
          );
        }
        setStep2Info((prev) => ({
          ...prev,
          feedback_notes: response.invoice.feedback_notes,
        }));
      } else if (step === STEPS.SIGN_REVIEW_SERVICE) {
        // For service agreement step, we need to send specific fields
        const payload = {
          initials: data.initial,
          signature: data?.signature || step2Info?.signature,
          feedback_notes: data.feedback,
          attachment_url: data.attachment_url || "",
        };

        const response = await sdk.callRawAPI(
          `/v3/api/custom/equality_record/subscription/public/invoice/${invoiceData?.invoice?.id}/${token}`,
          payload,
          "PUT"
        );

        if (response.error) {
          throw new Error(
            response.message || "Failed to update signature details"
          );
        }
        setStep2Info((prev) => ({
          ...prev,
          feedback_notes: response.invoice.feedback_notes,
          signature: response.invoice.signature,
          initial: response.invoice.initials,
          attachment_url: response.invoice.attachment_url,
        }));
      } else if (step === STEPS.CLIENT_DETAILS) {
        // Client details are handled in the parent component
        console.log("Client details updated");
      }
    } catch (error) {
      console.error("Failed to update step completion:", error);
      showToast(
        globalDispatch,
        error.message || "Failed to update",
        4000,
        "error"
      );
    }
  };

  const clearSignature = () => {
    if (signaturePadRef.current) {
      signaturePadRef.current.clear();
    }
  };

  const saveSignature = async () => {
    try {
      if (signaturePadRef.current && !signaturePadRef.current.isEmpty()) {
        const signatureDataUrl = signaturePadRef.current.toDataURL();

        // Convert data URL to blob
        const signatureBlob = await fetch(signatureDataUrl).then((r) =>
          r.blob()
        );

        // Upload the signature to S3 with signature file type
        const signatureUrl = await uploadFileToS3(signatureBlob, "signature");

        if (signatureUrl) {
          setSignature(signatureUrl);
          return signatureUrl; // Return the uploaded signature URL
        } else {
          throw new Error("Failed to upload signature");
        }
      }
      return null;
    } catch (error) {
      console.error("Error saving signature:", error);
      showToast(globalDispatch, "Failed to save signature", 4000, "error");
      return null;
    }
  };

  // This function is used for uploading files to S3
  const uploadFileToS3 = async (file, fileType = "document") => {
    try {
      if (!file) {
        return null;
      }

      // Generate a specific filename based on type and invoice ID with randomized numbers
      const timestamp = new Date().getTime();
      const randomNum = Math.floor(Math.random() * 1000000); // Generate random 6-digit number
      const invoiceId = invoiceData?.invoice?.id || "new";
      let fileName;

      if (fileType === "signature") {
        fileName = `signature_${invoiceId}_${timestamp}_${randomNum}.png`;
      } else if (fileType === "agreement") {
        fileName = `agreement_${invoiceId}_${timestamp}_${randomNum}.png`;
      } else {
        fileName = `document_${invoiceId}_${timestamp}_${randomNum}.png`;
      }

      // Create a new file with the custom name
      const renamedFile = new File([file], fileName, { type: file.type });

      // Create form data and upload
      const formData = new FormData();
      formData.append("files", renamedFile);

      const uploadResult = await uploadS3FilesAPI(formData);

      if (!uploadResult.error) {
        // Parse the attachments field to extract the URL
        const attachments = JSON.parse(uploadResult.attachments);
        const fileUrl = attachments[0];
        return fileUrl; // Return the uploaded file URL
      } else {
        throw new Error("Failed to upload file");
      }
    } catch (error) {
      console.error("Error uploading file:", error);
      showToast(globalDispatch, "Failed to upload file", 4000, "error");
      return null;
    }
  };

  const validateSignatureStep = () => {
    if (!initial) {
      showToast(globalDispatch, "Please enter your initials", 4000, "error");
      return false;
    }

    if (signaturePadRef.current?.isEmpty()) {
      showToast(globalDispatch, "Please provide your signature", 4000, "error");
      return false;
    }

    return true;
  };

  // We're now setting initial directly in the input onChange handler

  const handleCardPayment = async () => {
    if (!stripe || !elements) {
      setPaymentError("Stripe has not been initialized");
      return null;
    }

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      setPaymentError("Card element not found");
      return null;
    }

    try {
      const { error, paymentMethod } = await stripe.createPaymentMethod({
        type: "card",
        card: cardElement,
      });

      if (error) {
        setPaymentError(error.message);
        return null;
      }

      setPaymentMethodId(paymentMethod.id);
      setPaymentError(null);
      return paymentMethod.id;
    } catch (err) {
      setPaymentError("Failed to process card. Please try again.");
      return null;
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      default:
      case STEPS.INVOICE_LIST_QUOTE:
        return (
          <div className="p-6 rounded-lg border border-strokedark bg-boxdark">
            <h2 className="mb-6 text-2xl font-semibold text-white">
              Invoice/Quote
            </h2>

            {/* Company Information */}
            <div className="p-6 mb-8 rounded border border-stroke bg-meta-4">
              <div className="flex gap-6 items-center">
                <div className="overflow-hidden w-20 h-20 rounded-lg border border-stroke">
                  <img
                    src={
                      companyInfo?.company_logo !== "null"
                        ? companyInfo?.company_logo
                        : invoiceData?.invoice?.created_by_company_logo ||
                          "/placeholder-logo.png"
                    }
                    alt="Company Logo"
                    className="object-contain w-full h-full"
                  />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">
                    {companyInfo?.company_name ||
                      invoiceData?.invoice?.created_by_company_name ||
                      "Company Name"}
                  </h2>
                  <p className="text-bodydark">
                    {companyInfo?.office_email || ""}
                  </p>
                  {companyInfo?.phone && (
                    <p className="text-bodydark">{companyInfo.phone}</p>
                  )}
                  {companyInfo?.company_address && (
                    <p className="text-bodydark">
                      {companyInfo.company_address}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Invoice Items */}
            <div className="mb-8">
              <h3 className="mb-4 text-xl font-semibold text-white">
                Invoice Items
              </h3>
              <div className="overflow-x-auto">
                <table className="w-full table-auto">
                  <thead className="bg-meta-4">
                    <tr>
                      <th className="px-4 py-3 text-sm font-medium text-left text-white">
                        Mix Date
                      </th>
                      <th className="px-4 py-3 text-sm font-medium text-left text-white">
                        Producer
                      </th>
                      <th className="px-4 py-3 text-sm font-medium text-left text-white">
                        Mix Package
                      </th>
                      <th className="px-4 py-3 text-sm font-medium text-left text-white">
                        Team Name
                      </th>
                      <th className="px-4 py-3 text-sm font-medium text-left text-white">
                        Division
                      </th>
                      <th className="px-4 py-3 text-sm font-medium text-right text-white">
                        Amount
                      </th>
                      <th className="px-4 py-3 text-sm font-medium text-right text-white">
                        Discount
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoiceData?.items?.map((item, index) => (
                      <tr key={index} className="border-b border-stroke/50">
                        <td className="px-4 py-3 text-white">
                          {item.mix_date
                            ? new Date(item.mix_date).toLocaleDateString()
                            : "-"}
                        </td>
                        <td className="px-4 py-3 text-white">
                          {item.producers && item.producers.length > 0
                            ? item.producers[0]
                            : "-"}
                        </td>
                        <td className="px-4 py-3 text-white">
                          {item.description
                            ? item.description.split(" - ").pop()
                            : item.is_special
                            ? item.special_type
                            : "-"}
                        </td>
                        <td className="px-4 py-3 text-white">
                          {item.team_name || item.special_type}
                        </td>
                        <td className="px-4 py-3 text-white">
                          {item.division}
                        </td>
                        <td className="px-4 py-3 text-right text-white">
                          ${parseFloat(item.total).toFixed(2)}
                        </td>
                        <td className="px-4 py-3 text-right text-white">
                          {item.discount}%
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot className="bg-meta-4">
                    <tr>
                      <td
                        colSpan="6"
                        className="px-4 py-3 font-medium text-right text-white"
                      >
                        Subtotal:
                      </td>
                      <td className="px-4 py-3 font-medium text-right text-white">
                        $
                        {parseFloat(
                          invoiceData?.invoice?.subtotal || 0
                        ).toFixed(2)}
                      </td>
                    </tr>
                    <tr>
                      <td
                        colSpan="6"
                        className="px-4 py-3 font-medium text-right text-white"
                      >
                        Amount Remaining:
                      </td>
                      <td className="px-4 py-3 font-medium text-right text-white">
                        $ $
                        {(
                          parseFloat(invoiceData?.invoice?.total || 0) -
                          parseFloat(invoiceData?.invoice?.payment_amount || 0)
                        ).toFixed(2)}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            {/* Producer Notes */}
            <div className="mb-8">
              <h3 className="mb-4 text-xl font-semibold text-white">
                Producer Notes
              </h3>
              <div className="max-h-[200px] min-h-[100px] w-full overflow-y-auto rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 text-white">
                <div
                  className="max-w-none prose prose-invert"
                  dangerouslySetInnerHTML={{
                    __html: invoiceData?.invoice?.notes || "No producer notes",
                  }}
                />
              </div>
            </div>

            {/* Feedback Section */}
            <div className="mb-8">
              <h3 className="mb-4 text-lg font-medium text-white">
                Feedback & Notes
              </h3>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                className="w-full rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 text-white outline-none transition focus:border-primary active:border-primary"
                placeholder="Any feedback or notes about the invoice"
                rows={4}
              />
            </div>

            <div className="flex justify-between">
              <button
                onClick={async () => {
                  try {
                    // Update the feedback notes
                    await updateStepCompletion(STEPS.INVOICE_LIST_QUOTE, {
                      feedback: feedback,
                    });

                    // Show toast message
                    showToast(
                      globalDispatch,
                      "Invoice declined. Feedback sent to producer.",
                      4000,
                      "success"
                    );
                  } catch (error) {
                    console.error("Error updating feedback:", error);
                    showToast(
                      globalDispatch,
                      "Failed to send feedback",
                      4000,
                      "error"
                    );
                  }
                }}
                className="px-6 py-2 text-white rounded-md border border-danger bg-danger hover:bg-opacity-90"
              >
                Decline
              </button>
              <button
                onClick={() => setCurrentStep(STEPS.TERMS_AGREEMENT)}
                className="px-6 py-2 text-white rounded-md bg-primary hover:bg-opacity-90"
              >
                Accept & Continue
              </button>
            </div>
          </div>
        );

      case STEPS.TERMS_AGREEMENT:
        return (
          <div className="p-6 rounded-lg border border-strokedark bg-boxdark">
            <h2 className="mb-6 text-2xl font-semibold text-white">
              General Agreement
            </h2>

            {/* Terms and Conditions */}
            <div className="mb-8">
              <h3 className="mb-4 text-lg font-medium text-white">
                General Terms and Conditions
              </h3>
              <div className="mb-4 h-[calc(100vh-500px)] overflow-y-auto rounded bg-meta-4 p-4">
                <div
                  className="max-w-none prose prose-invert"
                  dangerouslySetInnerHTML={{
                    __html:
                      legalDocuments?.termsOfService ||
                      `
                      <h4>1. General Terms</h4>
                      <p>This agreement outlines the general terms and conditions between the client and the service provider.</p>
                      <h4>2. Payment Terms</h4>
                      <p>Payment is due according to the terms specified in the invoice. Late payments may incur additional fees.</p>
                      <h4>3. Delivery</h4>
                      <p>Services will be delivered according to the timeline specified in the invoice or as agreed upon separately.</p>
                      <h4>4. Modifications</h4>
                      <p>Any modifications to the services must be agreed upon in writing by both parties.</p>
                      <h4>5. Termination</h4>
                      <p>Either party may terminate this agreement with written notice if the other party breaches any material term.</p>
                    `,
                  }}
                />
              </div>
            </div>

            <div className="mb-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="agree-terms"
                  className="mr-3 w-5 h-5"
                  checked={termsAgreed}
                  onChange={(e) => setTermsAgreed(e.target.checked)}
                />
                <label htmlFor="agree-terms" className="text-white">
                  I have read and agree to the General Terms and Conditions
                </label>
              </div>
            </div>

            <div className="flex justify-between">
              <button
                onClick={() => setCurrentStep(STEPS.INVOICE_LIST_QUOTE)}
                className="px-6 py-2 text-white rounded-md border border-stroke hover:bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={() => {
                  if (!termsAgreed) {
                    showToast(
                      globalDispatch,
                      "Please agree to the Terms and Conditions to continue",
                      4000,
                      "error"
                    );
                    return;
                  }
                  setCurrentStep(STEPS.PRIVACY_POLICY);
                }}
                className="px-6 py-2 text-white rounded-md bg-primary hover:bg-opacity-90"
              >
                Next: Privacy Policy
              </button>
            </div>
          </div>
        );

      case STEPS.PRIVACY_POLICY:
        return (
          <div className="p-6 rounded-lg border border-strokedark bg-boxdark">
            <h2 className="mb-6 text-2xl font-semibold text-white">
              Privacy Policy
            </h2>

            {/* Privacy Policy Content */}
            <div className="mb-8">
              <div className="overflow-y-auto p-4 mb-4 max-h-60 rounded bg-meta-4">
                <div
                  className="max-w-none prose prose-invert"
                  dangerouslySetInnerHTML={{
                    __html:
                      legalDocuments?.privacyPolicy ||
                      invoiceData?.company_info?.privacy_policy ||
                      `
                        <h4>Privacy Policy</h4>
                        <p>This Privacy Policy describes how your personal information is collected, used, and shared when you use our services.</p>

                        <h4>1. Information We Collect</h4>
                        <p>We collect information you provide directly to us, such as your name, email address, phone number, and payment information.</p>

                        <h4>2. How We Use Your Information</h4>
                        <p>We use the information we collect to provide, maintain, and improve our services, to process transactions, and to communicate with you.</p>

                        <h4>3. Sharing Your Information</h4>
                        <p>We may share your information with service providers who perform services on our behalf, such as payment processing and data analysis.</p>

                        <h4>4. Data Security</h4>
                        <p>We take reasonable measures to help protect your personal information from loss, theft, misuse, and unauthorized access.</p>

                        <h4>5. Your Rights</h4>
                        <p>You have the right to access, correct, or delete your personal information. You may also have the right to object to or restrict certain processing of your data.</p>
                      `,
                  }}
                />
              </div>
            </div>

            <div className="mb-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="agree-privacy"
                  className="mr-3 w-5 h-5"
                  checked={privacyAgreed}
                  onChange={(e) => setPrivacyAgreed(e.target.checked)}
                />
                <label htmlFor="agree-privacy" className="text-white">
                  I have read and agree to the Privacy Policy
                </label>
              </div>
            </div>

            <div className="flex justify-between">
              <button
                onClick={() => setCurrentStep(STEPS.TERMS_AGREEMENT)}
                className="px-6 py-2 text-white rounded-md border border-stroke hover:bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={() => {
                  if (!privacyAgreed) {
                    showToast(
                      globalDispatch,
                      "Please agree to the Privacy Policy to continue",
                      4000,
                      "error"
                    );
                    return;
                  }
                  setCurrentStep(STEPS.SIGN_REVIEW_SERVICE);
                }}
                className="px-6 py-2 text-white rounded-md bg-primary hover:bg-opacity-90"
              >
                Next: Service Agreement
              </button>
            </div>
          </div>
        );

      case STEPS.SIGN_REVIEW_SERVICE:
        return (
          <div className="p-6 rounded-lg border border-strokedark bg-boxdark">
            <h2 className="mb-6 text-2xl font-semibold text-white">
              Review & Sign Service Agreement
            </h2>

            {/* Service Agreement */}
            <div className="mb-8">
              <h3 className="mb-4 text-lg font-medium text-white">
                Service Agreement
              </h3>
              <div className="mb-4  h-[calc(100vh-500px)] overflow-y-auto rounded bg-meta-4 p-4">
                <div
                  className="max-w-none prose prose-invert"
                  dangerouslySetInnerHTML={{
                    __html:
                      serviceAgreementContent ||
                      "I hereby approve this invoice",
                  }}
                />
              </div>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block mb-2 text-white">Signature</label>
                  {signature ? (
                    <div className="relative h-[150px] rounded border border-stroke bg-white p-2">
                      <img
                        src={`${invoiceData?.invoice?.signature || signature}`}
                        alt="Signature"
                        className="h-[110px] w-full object-contain"
                      />
                      <button
                        onClick={() => {
                          setSignature(null);
                          if (signaturePadRef.current) {
                            signaturePadRef.current.clear();
                          }
                          // Ensure the signature pad is displayed
                          signaturePadRef.current._canvas.style.display =
                            "block";
                        }}
                        className="mt-2 text-sm text-black cursor-pointer hover:text-black"
                      >
                        Edit
                      </button>
                    </div>
                  ) : (
                    <div className="h-[150px] rounded border border-stroke bg-white p-2">
                      <SignaturePad
                        ref={signaturePadRef}
                        canvasProps={{
                          className: "w-full h-[110px]",
                        }}
                      />
                      <button
                        onClick={clearSignature}
                        className="mt-2 text-sm text-black cursor-pointer hover:text-black"
                      >
                        Clear
                      </button>
                    </div>
                  )}
                </div>
                <div>
                  <label className="block mb-2 text-white">Initial</label>
                  <input
                    type="text"
                    value={initial}
                    onChange={(e) => setInitial(e.target.value)}
                    className="h-[150px] w-full rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 align-middle text-5xl font-bold text-white outline-none transition focus:border-primary active:border-primary"
                    placeholder="Enter your initials"
                    maxLength={10}
                  />
                </div>
              </div>
            </div>

            {/* Feedback Section */}
            <div className="mb-8">
              <h3 className="mb-4 text-lg font-medium text-white">
                Feedback & Notes
              </h3>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                className="w-full rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 text-white outline-none transition focus:border-primary active:border-primary"
                placeholder="Any feedback or notes about the invoice"
                rows={4}
              />
            </div>
            {console.log("InvoiceMultiStep - Company Info:", {
              companyInfo,
              license_company_logo: companyInfo?.license_company_logo,
              company_logo: companyInfo?.company_logo,
              signature,
            })}
            {/* Generate PDF Button */}
            <div className="mb-8">
              {/* Hidden Service Agreement PDF Component */}
              <ServiceAgreementPDF
                id={invoiceData?.invoice?.id}
                invoiceId={invoiceData?.invoice?.id}
                companyLogo={
                  companyInfo?.company_logo ||
                  invoiceData?.invoice?.created_by_company_logo ||
                  ""
                }
                licenseCompanyLogo={
                  companyInfo?.license_company_logo ||
                  invoiceData?.invoice?.created_by_license_company_logo ||
                  ""
                }
                companyName={
                  companyInfo?.company_name ||
                  invoiceData?.invoice?.created_by_company_name ||
                  ""
                }
                companyEmail={companyInfo?.office_email || ""}
                companyPhone={companyInfo?.phone || ""}
                companyAddress={companyInfo?.company_address || ""}
                serviceAgreementContent={
                  serviceAgreementContent || "I hereby approve this invoice"
                }
                signature={signature}
                initial={initial}
                clientName={`${watch().firstName || ""} ${
                  watch().lastName || ""
                }`}
              />

              <button
                onClick={async () => {
                  try {
                    setGeneratingPdf(true);
                    if (!validateSignatureStep()) {
                      return;
                    }

                    // Check if service agreement content is valid
                    if (
                      !serviceAgreementContent ||
                      serviceAgreementContent === "null" ||
                      serviceAgreementContent === "undefined"
                    ) {
                      showToast(
                        globalDispatch,
                        "Service agreement content is missing or invalid",
                        4000,
                        "error"
                      );
                      return;
                    }

                    // Save signature first if not already saved
                    let uploadedSignature = signature;
                    if (
                      !uploadedSignature &&
                      signaturePadRef.current &&
                      !signaturePadRef.current.isEmpty()
                    ) {
                      uploadedSignature = await saveSignature();
                    }

                    // Get the PDF element
                    const pdfElement = document.getElementById(
                      `printable-service-agreement-${invoiceData?.invoice?.id}`
                    );

                    if (!pdfElement) {
                      throw new Error("PDF element not found");
                    }

                    // Use html2canvas to convert the element to a canvas
                    const canvas = await html2canvas(pdfElement, {
                      scale: 3, // Higher scale for better quality
                      useCORS: false, // Disable CORS since we're using base64 images
                      logging: false, // Disable logging for cleaner console
                      backgroundColor: "#FFFFFF",
                      allowTaint: true, // Allow tainted canvas since we're using base64
                      imageTimeout: 15000,
                    });

                    // Convert canvas to blob with high quality
                    const blob = await new Promise((resolve) => {
                      canvas.toBlob(
                        (blob) => {
                          resolve(blob);
                        },
                        "image/png",
                        1.0 // Maximum quality
                      );
                    });

                    // Upload the PDF to S3 with agreement file type
                    const pdfUrl = await uploadFileToS3(blob, "agreement");

                    if (pdfUrl) {
                      // Store the generated PDF URL for download link
                      setGeneratedPdfUrl(pdfUrl);

                      // Update the step with all required fields including the PDF URL
                      await updateStepCompletion(STEPS.SIGN_REVIEW_SERVICE, {
                        initial: initial,
                        signature: uploadedSignature,
                        feedback: feedback,
                        attachment_url: pdfUrl,
                      });

                      showToast(
                        globalDispatch,
                        "Service agreement PDF generated and saved successfully",
                        4000,
                        "success"
                      );
                    } else {
                      throw new Error("Failed to upload service agreement PDF");
                    }
                  } catch (error) {
                    console.error("Error generating PDF:", error);
                    showToast(
                      globalDispatch,
                      "Failed to generate service agreement PDF",
                      4000,
                      "error"
                    );
                  } finally {
                    setGeneratingPdf(false);
                  }
                }}
                className="px-6 py-3 w-full text-white rounded-md bg-success hover:bg-opacity-90"
                disabled={generatingPdf}
              >
                {generatingPdf
                  ? "Generating..."
                  : "Generate Service Agreement PDF"}
              </button>

              {/* Download Link for Generated PDF */}
              {generatedPdfUrl && (
                <div className="mt-4 text-center">
                  <a
                    href={`${generatedPdfUrl}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    <svg
                      className="mr-2 w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    Download Service Agreement PDF
                  </a>
                </div>
              )}
            </div>

            <div className="flex justify-between">
              <button
                onClick={() => setCurrentStep(STEPS.PRIVACY_POLICY)}
                className="px-6 py-2 text-white rounded-md border border-stroke hover:bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={async () => {
                  try {
                    setGeneratingPdf(true);
                    if (!validateSignatureStep()) {
                      setGeneratingPdf(false);
                      return;
                    }

                    // Check if service agreement content is valid
                    if (
                      !serviceAgreementContent ||
                      serviceAgreementContent === "null" ||
                      serviceAgreementContent === "undefined"
                    ) {
                      showToast(
                        globalDispatch,
                        "Service agreement content is missing or invalid",
                        4000,
                        "error"
                      );
                      setGeneratingPdf(false);
                      return;
                    }

                    // Save signature first if not already saved
                    let uploadedSignature = signature;
                    if (
                      !uploadedSignature &&
                      signaturePadRef.current &&
                      !signaturePadRef.current.isEmpty()
                    ) {
                      uploadedSignature = await saveSignature();
                    }

                    // Check if we have an attachment_url (either existing or generated)
                    const existingAttachmentUrl =
                      step2Info?.attachment_url ||
                      invoiceData?.invoice?.attachment_url ||
                      generatedPdfUrl;

                    // If no PDF exists, generate one automatically
                    let finalAttachmentUrl = existingAttachmentUrl;
                    if (!existingAttachmentUrl) {
                      showToast(
                        globalDispatch,
                        "Generating service agreement PDF...",
                        3000,
                        "info"
                      );

                      // Get the PDF element
                      const pdfElement = document.getElementById(
                        `printable-service-agreement-${invoiceData?.invoice?.id}`
                      );

                      if (!pdfElement) {
                        throw new Error("PDF element not found");
                      }

                      // Use html2canvas to convert the element to a canvas
                      const canvas = await html2canvas(pdfElement, {
                        scale: 3,
                        useCORS: false,
                        logging: false,
                        backgroundColor: "#FFFFFF",
                        allowTaint: true,
                        imageTimeout: 15000,
                      });

                      // Convert canvas to blob with high quality
                      const blob = await new Promise((resolve) => {
                        canvas.toBlob(
                          (blob) => {
                            resolve(blob);
                          },
                          "image/png",
                          1.0
                        );
                      });

                      // Upload the PDF to S3 with agreement file type
                      finalAttachmentUrl = await uploadFileToS3(
                        blob,
                        "agreement"
                      );

                      if (finalAttachmentUrl) {
                        setGeneratedPdfUrl(finalAttachmentUrl);
                        showToast(
                          globalDispatch,
                          "Service agreement PDF generated successfully",
                          3000,
                          "success"
                        );
                      } else {
                        throw new Error(
                          "Failed to generate service agreement PDF"
                        );
                      }
                    }

                    // Update the step with all required fields
                    await updateStepCompletion(STEPS.SIGN_REVIEW_SERVICE, {
                      initial: initial,
                      signature: uploadedSignature,
                      feedback: feedback,
                      attachment_url: finalAttachmentUrl,
                    });

                    // Move to next step
                    setGeneratingPdf(false);
                    setCurrentStep(STEPS.CLIENT_DETAILS);
                  } catch (error) {
                    setGeneratingPdf(false);
                    console.error("Error updating signature details:", error);
                    showToast(
                      globalDispatch,
                      "Failed to save signature details",
                      4000,
                      "error"
                    );
                  }
                }}
                className="px-6 py-2 text-white rounded-md bg-primary hover:bg-opacity-90"
                disabled={generatingPdf}
              >
                {generatingPdf ? "Processing..." : "Next: Client Information"}
              </button>
            </div>
          </div>
        );

      case STEPS.CLIENT_DETAILS:
        return (
          <div className="p-6 rounded-lg border border-strokedark bg-boxdark">
            {/* Company Information */}
            <div className="p-6 mb-8 rounded border border-stroke bg-meta-4">
              <div className="flex gap-6 items-center">
                <div className="overflow-hidden w-20 h-20 rounded-lg border border-stroke">
                  <img
                    src={
                      companyInfo?.company_logo !== "null"
                        ? companyInfo?.company_logo
                        : invoiceData?.invoice?.created_by_company_logo ||
                          "/placeholder-logo.png"
                    }
                    alt="Company Logo"
                    className="object-contain w-full h-full"
                  />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">
                    {companyInfo?.company_name ||
                      invoiceData?.invoice?.created_by_company_name ||
                      "Company Name"}
                  </h2>
                  <p className="text-bodydark">
                    {companyInfo?.office_email || ""}
                  </p>
                  {companyInfo?.phone && (
                    <p className="text-bodydark">{companyInfo.phone}</p>
                  )}
                  {companyInfo?.company_address && (
                    <p className="text-bodydark">
                      {companyInfo.company_address}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <h2 className="mb-6 text-2xl font-semibold text-white">
              Client Information
            </h2>

            {/* Client Details Form */}
            <form className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label className="mb-2.5 block font-medium text-white">
                  First Name
                </label>
                <input
                  {...register("firstName")}
                  className="w-full rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 text-white outline-none transition focus:border-primary active:border-primary"
                  placeholder="Enter first name"
                />
                {errors.firstName && (
                  <p className="mt-1 text-xs text-danger">
                    {errors.firstName.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2.5 block font-medium text-white">
                  Last Name
                </label>
                <input
                  {...register("lastName")}
                  className="w-full rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 text-white outline-none transition focus:border-primary active:border-primary"
                  placeholder="Enter last name"
                />
                {errors.lastName && (
                  <p className="mt-1 text-xs text-danger">
                    {errors.lastName.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2.5 block font-medium text-white">
                  Email
                </label>
                <input
                  {...register("email")}
                  type="email"
                  className="w-full rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 text-white outline-none transition focus:border-primary active:border-primary"
                  placeholder="Enter email address"
                />
                {errors.email && (
                  <p className="mt-1 text-xs text-danger">
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2.5 block font-medium text-white">
                  Phone
                </label>
                <input
                  {...register("phone")}
                  type="tel"
                  className="w-full rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 text-white outline-none transition focus:border-primary active:border-primary"
                  placeholder="Enter phone number"
                />
                {errors.phone && (
                  <p className="mt-1 text-xs text-danger">
                    {errors.phone.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2.5 block font-medium text-white">
                  Position
                </label>
                <input
                  {...register("position")}
                  className="w-full rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 text-white outline-none transition focus:border-primary active:border-primary"
                  placeholder="Enter position"
                />
                {errors.position && (
                  <p className="mt-1 text-xs text-danger">
                    {errors.position.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2.5 block font-medium text-white">
                  Program
                </label>
                <input
                  {...register("program")}
                  className="w-full rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 text-white outline-none transition focus:border-primary active:border-primary"
                  placeholder="Enter program"
                />
                {errors.program && (
                  <p className="mt-1 text-xs text-danger">
                    {errors.program.message}
                  </p>
                )}
              </div>

              {/* Invoice Items */}
              <div className="col-span-2 mt-8">
                <h3 className="mb-4 text-xl font-semibold text-white">
                  Invoice Items
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full table-auto">
                    <thead className="bg-meta-4">
                      <tr>
                        {/* <th className="px-4 py-3 text-sm font-medium text-left text-white">
                          Description
                        </th> */}
                        <th className="px-4 py-3 text-sm font-medium text-left text-white">
                          Mix Date
                        </th>
                        <th className="px-4 py-3 text-sm font-medium text-left text-white">
                          Producer
                        </th>
                        <th className="px-4 py-3 text-sm font-medium text-left text-white">
                          Mix Package
                        </th>
                        <th className="px-4 py-3 text-sm font-medium text-left text-white">
                          Team Name
                        </th>
                        <th className="px-4 py-3 text-sm font-medium text-left text-white">
                          Division
                        </th>
                        <th className="px-4 py-3 text-sm font-medium text-left text-white">
                          Survey Due
                        </th>
                        <th className="px-4 py-3 text-sm font-medium text-left text-white">
                          Submission Due
                        </th>
                        <th className="px-4 py-3 text-sm font-medium text-left text-white">
                          Est. Completion
                        </th>
                        <th className="px-4 py-3 text-sm font-medium text-right text-white">
                          Amount
                        </th>
                        <th className="px-4 py-3 text-sm font-medium text-right text-white">
                          Discount
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {invoiceData?.items?.map((item, index) => (
                        <tr key={index} className="border-b border-stroke/50">
                          <td className="px-4 py-3 text-white">
                            {item.mix_date
                              ? new Date(item.mix_date).toLocaleDateString()
                              : "-"}
                          </td>
                          <td className="px-4 py-3 text-white">
                            {item.producers && item.producers.length > 0
                              ? item.producers[0]
                              : "-"}
                          </td>
                          <td className="px-4 py-3 text-white">
                            {item.description
                              ? item.description.split(" - ").pop()
                              : item.is_special
                              ? item.special_type
                              : "-"}
                          </td>
                          <td className="px-4 py-3 text-white">
                            {item.team_name || item.special_type}
                          </td>
                          <td className="px-4 py-3 text-white">
                            {item.division}
                          </td>
                          <td className="px-4 py-3 text-white">
                            {item.music_survey_due
                              ? new Date(
                                  item.music_survey_due
                                ).toLocaleDateString()
                              : "-"}
                          </td>
                          <td className="px-4 py-3 text-white">
                            {item.routine_submission_due
                              ? new Date(
                                  item.routine_submission_due
                                ).toLocaleDateString()
                              : "-"}
                          </td>
                          <td className="px-4 py-3 text-white">
                            {item.estimated_completion
                              ? new Date(
                                  item.estimated_completion
                                ).toLocaleDateString()
                              : "-"}
                          </td>
                          <td className="px-4 py-3 text-right text-white">
                            ${parseFloat(item.total).toFixed(2)}
                          </td>

                          <td className="px-4 py-3 text-right text-white">
                            {item.discount}%
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot className="bg-meta-4">
                      <tr>
                        <td
                          colSpan="9"
                          className="px-4 py-3 font-medium text-right text-white"
                        >
                          Subtotal:
                        </td>
                        <td className="px-4 py-3 font-medium text-right text-white">
                          $
                          {parseFloat(
                            invoiceData?.invoice?.subtotal || 0
                          ).toFixed(2)}
                        </td>
                      </tr>
                      <tr>
                        <td
                          colSpan="9"
                          className="px-4 py-3 font-medium text-right text-white"
                        >
                          Amount Remaining:
                        </td>
                        <td className="px-4 py-3 font-medium text-right text-white">
                          $
                          {(
                            parseFloat(invoiceData?.invoice?.total || 0) -
                            parseFloat(
                              invoiceData?.invoice?.payment_amount || 0
                            )
                          ).toFixed(2)}
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </form>

            <div className="flex col-span-2 justify-between mt-6">
              <button
                onClick={() => setCurrentStep(STEPS.SIGN_REVIEW_SERVICE)}
                className="px-6 py-2 text-white rounded-md border border-stroke hover:bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={async () => {
                  const formData = watch();
                  // Update client details before moving to next step
                  console.log(formData);
                  if (invoiceData?.invoice?.client_id) {
                    const clientUpdateData = {
                      first_name: formData.firstName,
                      last_name: formData.lastName,
                      email: formData.email,
                      phone: formData.phone,
                      position: formData.position,
                      program: formData.program,
                    };
                    await onSubmit({
                      clientDetails: clientUpdateData,
                      step: STEPS.CLIENT_DETAILS,
                    });
                  }
                  await updateStepCompletion(STEPS.CLIENT_DETAILS, formData);
                  setCurrentStep(STEPS.PAYMENT);
                }}
                className="px-6 py-2 text-white rounded-md bg-primary hover:bg-opacity-90"
              >
                Next: Payment
              </button>
            </div>
          </div>
        );

      case STEPS.PAYMENT:
        return (
          <div className="p-6 rounded-lg border border-strokedark bg-boxdark">
            <h2 className="mb-6 text-2xl font-semibold text-white">Payment</h2>

            {/* Invoice Summary */}
            <div className="mb-8">
              <h3 className="mb-4 text-lg font-medium text-white">
                Invoice Summary
              </h3>
              <div className="overflow-x-auto rounded-lg border border-stroke bg-meta-4/20">
                <table className="w-full table-auto">
                  <thead className="bg-meta-4">
                    <tr>
                      <th className="px-4 py-3 text-sm font-medium text-left text-white">
                        Description
                      </th>
                      <th className="px-4 py-3 text-sm font-medium text-left text-white">
                        Team Name
                      </th>
                      <th className="px-4 py-3 text-sm font-medium text-right text-white">
                        Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoiceData.items?.map((item, index) => (
                      <tr key={index} className="border-b border-stroke/50">
                        <td className="px-4 py-3 text-white">
                          <div>{item.description || "Mix Service"}</div>
                          {item.producers && item.producers.length > 0 && (
                            <div className="text-xs text-bodydark2">
                              Producer: {item.producers[0]}
                            </div>
                          )}
                        </td>
                        <td className="px-4 py-3 text-white">
                          {item.team_name || "Additional Charge"}
                        </td>
                        <td className="px-4 py-3 text-right text-white">
                          ${parseFloat(item.total).toFixed(2)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="bg-meta-4/50">
                      <td
                        colSpan="2"
                        className="px-4 py-3 font-medium text-right text-white"
                      >
                        Subtotal:
                      </td>
                      <td className="px-4 py-3 font-medium text-right text-white">
                        $
                        {parseFloat(
                          invoiceData?.invoice?.subtotal || 0
                        ).toFixed(2)}
                      </td>
                    </tr>
                    <tr className="bg-meta-4/50">
                      <td
                        colSpan="2"
                        className="px-4 py-3 font-medium text-right text-white"
                      >
                        Amount Paid:
                      </td>
                      <td className="px-4 py-3 font-medium text-right text-white">
                        $
                        {parseFloat(
                          invoiceData?.invoice?.payment_amount || 0
                        ).toFixed(2)}
                      </td>
                    </tr>
                    <tr className="bg-meta-4/50">
                      <td
                        colSpan="2"
                        className="px-4 py-3 font-medium text-right text-white"
                      >
                        Total Due:
                      </td>
                      <td className="px-4 py-3 font-medium text-right text-white">
                        $
                        {parseFloat(invoiceData?.invoice?.total || 0).toFixed(
                          2
                        )}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            {/* Payment Options */}
            <div className="mb-8">
              <h3 className="mb-4 text-lg font-medium text-white">
                Payment Options
              </h3>

              {calculateRemainingAmount() <= 0 ? (
                <div className="p-4 rounded-lg border border-success bg-success/10">
                  <p className="text-lg font-medium text-success">
                    This invoice has been fully paid. No further payment is
                    required.
                  </p>
                </div>
              ) : (
                <div className="flex flex-col gap-4 p-4 rounded-lg border border-stroke bg-meta-4/20">
                  {calculateRemainingDeposit() > 0 && (
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="pay-deposit"
                        name="payment-option"
                        value="deposit"
                        checked={paymentType === "deposit"}
                        onChange={(e) => {
                          setPaymentType(e.target.value);
                        }}
                        className="mr-3 w-5 h-5"
                      />
                      <label htmlFor="pay-deposit" className="flex flex-col">
                        <span className="text-lg font-medium text-white">
                          Pay Deposit
                        </span>
                        <span className="text-sm text-bodydark2">
                          Pay the required deposit amount
                        </span>
                      </label>
                      <span className="ml-auto text-xl font-bold text-white">
                        ${calculateRemainingDeposit().toFixed(2)}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="pay-full"
                      name="payment-option"
                      value="full"
                      checked={paymentType === "full"}
                      onChange={(e) => {
                        setPaymentType(e.target.value);
                      }}
                      className="mr-3 w-5 h-5"
                    />
                    <label htmlFor="pay-full" className="flex flex-col">
                      <span className="text-lg font-medium text-white">
                        Pay Remaining Amount
                      </span>
                      <span className="text-sm text-bodydark2">
                        Pay the remaining balance on this invoice
                      </span>
                    </label>
                    <span className="ml-auto text-xl font-bold text-white">
                      ${calculateRemainingAmount().toFixed(2)}
                    </span>
                  </div>

                  {calculateTotalPaid() > 0 && (
                    <div className="p-3 mt-2 rounded bg-meta-4/30">
                      <p className="text-sm text-bodydark">
                        <span className="font-medium">
                          Amount already paid:
                        </span>{" "}
                        $
                        {parseFloat(
                          invoiceData?.invoice?.payment_amount || 0
                        ).toFixed(2)}
                      </p>
                      <p className="text-sm text-bodydark">
                        <span className="font-medium">Original total:</span> $
                        {parseFloat(invoiceData?.invoice?.total || 0).toFixed(
                          2
                        )}
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Payment Method */}
            <div className="mb-8">
              <h3 className="mb-4 text-lg font-medium text-white">
                Payment Method
              </h3>
              <div className="p-4 rounded-lg border border-stroke bg-meta-4/20">
                <div className="mb-4">
                  <div className="flex gap-4 mb-4">
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="payment-stripe"
                        name="payment-method"
                        value="stripe"
                        checked={paymentMethod === "stripe"}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="mr-2 w-5 h-5"
                      />
                      <label htmlFor="payment-stripe" className="text-white">
                        Pay with Credit Card
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="payment-check"
                        name="payment-method"
                        value="check"
                        checked={paymentMethod === "check"}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="mr-2 w-5 h-5"
                      />
                      <label htmlFor="payment-check" className="text-white">
                        Pay with Check
                      </label>
                    </div>
                  </div>

                  {paymentMethod === "stripe" && (
                    <>
                      <label className="block mb-2 text-white">
                        Card Details
                      </label>
                      <div className="p-3 rounded border border-stroke bg-boxdark">
                        <CardElement
                          options={{
                            style: {
                              base: {
                                fontSize: "16px",
                                color: "#ffffff",
                                "::placeholder": {
                                  color: "#aab7c4",
                                },
                              },
                              invalid: {
                                color: "#dc3545",
                              },
                            },
                          }}
                        />
                      </div>
                      {paymentError && (
                        <p className="mt-2 text-sm text-danger">
                          {paymentError}
                        </p>
                      )}
                    </>
                  )}

                  {paymentMethod === "check" && (
                    <div className="mt-4">
                      <p className="text-white">
                        You have selected to pay by check. Please make your
                        check payable to{" "}
                        {companyInfo?.company_name ||
                          invoiceData?.invoice?.created_by_company_name ||
                          "Company Name"}
                        .
                      </p>
                    </div>
                  )}
                </div>

                <div className="mb-4">
                  <label className="block mb-2 text-white">
                    Payment Notes (Optional)
                  </label>
                  <textarea
                    value={paymentNotes}
                    onChange={(e) => setPaymentNotes(e.target.value)}
                    className="w-full rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 text-white outline-none transition focus:border-primary active:border-primary"
                    placeholder="Add any notes about this payment"
                    rows={2}
                  />
                </div>
              </div>
            </div>

            {/* Payment Summary */}
            <div className="p-4 mb-8 rounded-lg border border-stroke bg-meta-4/20">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-white">
                  Total to Pay:
                </h3>
                <p className="text-2xl font-bold text-white">
                  ${paymentAmount.toFixed(2)}
                </p>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <button
                onClick={() => setCurrentStep(STEPS.CLIENT_DETAILS)}
                className="px-6 py-2 text-white rounded-md border border-stroke hover:bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={async () => {
                  // Check if invoice is already fully paid
                  if (calculateRemainingAmount() <= 0) {
                    showToast(
                      globalDispatch,
                      "This invoice has already been fully paid",
                      5000,
                      "error"
                    );
                    return;
                  }

                  if (paymentAmount <= 0) {
                    showToast(
                      globalDispatch,
                      "Please select a payment option",
                      5000,
                      "error"
                    );
                    return;
                  }

                  try {
                    let paymentMethodId = null;

                    // Handle payment method specific logic
                    if (paymentMethod === "stripe") {
                      // Handle card payment
                      paymentMethodId = await handleCardPayment();
                      if (!paymentMethodId) {
                        return; // Stop if card processing failed
                      }
                    }

                    // Process payment for the entire invoice with new API structure
                    const payload = {
                      invoiceId: invoiceData.invoice.id,
                      paymentMethodId: paymentMethodId,
                      paymentType: paymentType, // "deposit", "full", or "custom"
                      paymentMethod: paymentMethod, // "check", "stripe", etc.
                      attachment_url: "",
                    };

                    await onSubmit({
                      type: "payment",
                      payload,
                      signature,
                      initial,
                      paymentNotes,
                      feedback,
                      card:
                        paymentMethod === "stripe"
                          ? { stripe: stripe, elements: elements }
                          : null,
                    });
                  } catch (error) {
                    console.error("Payment processing error:", error);
                    showToast(
                      globalDispatch,
                      "Payment processing failed. Please try again.",
                      4000,
                      "error"
                    );
                  }
                }}
                className="px-6 py-2 text-white rounded-md bg-primary hover:bg-opacity-90"
              >
                Complete Payment
              </button>
            </div>
          </div>
        );
    }
  };

  // Add breadcrumbs steps
  const steps = [
    { number: STEPS.INVOICE_LIST_QUOTE, label: "Invoice/Quote" },
    { number: STEPS.TERMS_AGREEMENT, label: "Terms & Agreement" },
    { number: STEPS.PRIVACY_POLICY, label: "Privacy Policy" },
    { number: STEPS.SIGN_REVIEW_SERVICE, label: "Service Agreement" },
    { number: STEPS.CLIENT_DETAILS, label: "Client Information" },
    { number: STEPS.PAYMENT, label: "Payment" },
  ];

  return (
    <div>
      {/* Breadcrumbs Navigation */}
      <div className="mb-8">
        <div className="flex justify-center items-center">
          {steps.map((step, index) => (
            <div key={step.number} className="flex items-center">
              <div
                className={`flex h-10 w-10 items-center justify-center rounded-full border-2 ${
                  currentStep >= step.number
                    ? "border-primary bg-primary text-white"
                    : "border-stroke bg-boxdark text-bodydark2"
                }`}
              >
                {step.number}
              </div>
              <div
                className={`mx-2 text-sm font-medium ${
                  currentStep >= step.number ? "text-white" : "text-bodydark2"
                }`}
              >
                {step.label}
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`h-px w-20 ${
                    currentStep > step.number ? "bg-primary" : "bg-stroke"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Existing Content */}
      {renderStep()}
    </div>
  );
};

export default InvoiceMultiStep;
