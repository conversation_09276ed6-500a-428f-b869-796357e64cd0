import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Src/globalContext";
import { AuthContext, tokenExpireError } from "Src/authContext";
import moment from "moment";
import { ClipLoader } from "react-spinners";
import {
  ArrowLeft,
  CreditCard as CreditCardIcon,
  X,
  RefreshCw,
  Download,
  FileText,
} from "lucide-react";
import { loadStripe } from "@stripe/stripe-js";
import {
  Elements,
  CardElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { getUserDetailsByIdAPI } from "Src/services/userService";

const stripePromise = loadStripe(
  "pk_test_51Ll5ukBgOlWo0lDUrBhA2W7EX2MwUH9AR5Y3KQoujf7PTQagZAJylWP1UOFbtH4UwxoufZbInwehQppWAq53kmNC00UIKSmebO"
);

// Custom Modal Component
const CustomModal = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="flex overflow-y-auto overflow-x-hidden fixed inset-0 z-50 justify-center items-center bg-black/50">
      <div className="relative max-h-[90vh] w-full max-w-md overflow-y-auto rounded-lg bg-boxdark p-4 shadow-lg">
        <div className="flex justify-between items-center pb-3 mb-4 border-b border-strokedark">
          <h3 className="text-xl font-semibold text-white">{title}</h3>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-meta-4"
          >
            <X className="w-5 h-5 text-white" />
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

// Payment Modal Component
const PaymentModal = ({
  isOpen,
  onClose,
  invoiceItemId,
  onPaymentComplete,
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);
    setErrorMessage("");

    try {
      // Step 1: Create payment intent
      const sdk = new MkdSDK();
      const paymentResponse = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/subscription/invoice-item/payment",
        { invoiceItemId },
        "POST"
      );

      if (paymentResponse.error) {
        throw new Error(
          paymentResponse.message || "Failed to initiate payment"
        );
      }

      // Step 2: Confirm card payment
      const { client_secret, payment_intent } = paymentResponse;
      const cardElement = elements.getElement(CardElement);

      const { error } = await stripe.confirmCardPayment(client_secret, {
        payment_method: {
          card: cardElement,
        },
      });

      if (error) {
        throw new Error(error.message);
      }

      const paymentIntent = payment_intent.id;

      // Step 3: Confirm payment with backend
      const confirmResponse = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/subscription/invoice-item/payment/confirm",
        { invoiceItemId, paymentIntentId: paymentIntent },
        "POST"
      );

      if (confirmResponse.error) {
        throw new Error(confirmResponse.message || "Failed to confirm payment");
      }

      showToast(globalDispatch, "Payment successful!", "success");
      onPaymentComplete();
      onClose();
    } catch (error) {
      console.error("Payment error:", error);
      setErrorMessage(error.message || "Payment failed. Please try again.");
      showToast(globalDispatch, error.message || "Payment failed", "error");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <CustomModal isOpen={isOpen} onClose={onClose} title="Complete Payment">
      <div className="p-4">
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium text-white">
              Card Details
            </label>
            <div className="p-3 rounded border border-strokedark bg-meta-4/10">
              <CardElement
                options={{
                  style: {
                    base: {
                      fontSize: "16px",
                      color: "#ffffff",
                      "::placeholder": {
                        color: "#aab7c4",
                      },
                    },
                    invalid: {
                      color: "#fa755a",
                      iconColor: "#fa755a",
                    },
                  },
                }}
              />
            </div>
          </div>

          {errorMessage && (
            <div className="p-3 mb-4 text-sm rounded bg-danger/10 text-danger">
              {errorMessage}
            </div>
          )}

          <div className="flex justify-end mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 mr-2 text-sm font-medium text-white rounded bg-meta-4 hover:bg-opacity-90"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!stripe || isProcessing}
              className="px-4 py-2 text-sm font-medium text-white rounded bg-primary hover:bg-opacity-90 disabled:bg-opacity-50"
            >
              {isProcessing ? "Processing..." : "Pay Now"}
            </button>
          </div>
        </form>
      </div>
    </CustomModal>
  );
};

// Pay All Modal Component - Similar to last step of InvoiceMultiStep
const PayAllModal = ({ isOpen, onClose, invoice, onPaymentComplete }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("stripe");
  const [paymentNotes, setPaymentNotes] = useState("");
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const remainingAmount =
    parseFloat(invoice?.invoice?.total || 0) -
    parseFloat(invoice?.invoice?.payment_amount || 0);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    if (remainingAmount <= 0) {
      showToast(
        globalDispatch,
        "This invoice has already been fully paid",
        "error"
      );
      return;
    }

    setIsProcessing(true);
    setErrorMessage("");

    try {
      const sdk = new MkdSDK();

      // Get the producer name from the first item's producers array if available
      let producerName = "";
      if (
        invoice?.items?.length > 0 &&
        invoice.items[0].producers?.length > 0
      ) {
        producerName = invoice.items[0].producers[0];
      }

      // Prepare the payload similar to InvoiceMultiStep
      const payload = {
        invoiceId: invoice.invoice.id,
        paymentMethodId: null,
        paymentType: "full", // Always pay full remaining amount
        paymentMethod: paymentMethod,
        attachment_url: "",
        producerName: producerName,
      };

      // Handle payment based on payment method
      if (paymentMethod === "stripe") {
        // Handle card payment
        const cardElement = elements.getElement(CardElement);
        if (!cardElement) {
          throw new Error("Card element not found");
        }

        const { error, paymentMethod: stripePaymentMethod } =
          await stripe.createPaymentMethod({
            type: "card",
            card: cardElement,
          });

        if (error) {
          throw new Error(error.message);
        }

        payload.paymentMethodId = stripePaymentMethod.id;

        // Process Stripe payment
        const response = await sdk.callRawAPI(
          `/v3/api/custom/equality_record/subscription/payment`,
          payload,
          "POST"
        );

        if (response.error) {
          throw new Error(response.message || "Payment failed");
        }

        const { client_secret, payment_id } = response;

        const { error: confirmError } = await stripe.confirmCardPayment(
          client_secret,
          {
            payment_method: {
              card: cardElement,
            },
          }
        );

        if (confirmError) {
          throw new Error(confirmError.message);
        }

        const confirmResponse = await sdk.callRawAPI(
          `/v3/api/custom/equality_record/subscription/payment/confirm`,
          { paymentId: payment_id },
          "POST"
        );

        if (confirmResponse?.error) {
          throw new Error(
            confirmResponse.message || "Payment confirmation failed"
          );
        }
      } else if (paymentMethod === "check") {
        // Process check payment
        const response = await sdk.callRawAPI(
          `/v3/api/custom/equality_record/subscription/payment`,
          payload,
          "POST"
        );

        if (response.error) {
          throw new Error(response.message || "Payment failed");
        }
      }

      showToast(globalDispatch, "Payment successful!", "success");
      onPaymentComplete();
      onClose();
    } catch (error) {
      console.error("Payment error:", error);
      setErrorMessage(error.message || "Payment failed. Please try again.");
      showToast(globalDispatch, error.message || "Payment failed", "error");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <CustomModal isOpen={isOpen} onClose={onClose} title="Pay Full Amount">
      <div className="p-4">
        {/* Payment Summary */}
        <div className="p-4 mb-6 rounded-lg border border-stroke bg-meta-4/20">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">Total to Pay:</h3>
            <p className="text-2xl font-bold text-white">
              ${remainingAmount.toFixed(2)}
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Payment Method */}
          <div className="mb-6">
            <h3 className="mb-4 text-lg font-medium text-white">
              Payment Method
            </h3>
            <div className="p-4 rounded-lg border border-stroke bg-meta-4/20">
              <div className="mb-4">
                <div className="flex gap-4 mb-4">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="payment-stripe"
                      name="payment-method"
                      value="stripe"
                      checked={paymentMethod === "stripe"}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="mr-2 w-5 h-5"
                    />
                    <label htmlFor="payment-stripe" className="text-white">
                      Pay with Credit Card
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="payment-check"
                      name="payment-method"
                      value="check"
                      checked={paymentMethod === "check"}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="mr-2 w-5 h-5"
                    />
                    <label htmlFor="payment-check" className="text-white">
                      Pay with Check
                    </label>
                  </div>
                </div>

                {paymentMethod === "stripe" && (
                  <>
                    <label className="block mb-2 text-white">
                      Card Details
                    </label>
                    <div className="p-3 rounded border border-stroke bg-boxdark">
                      <CardElement
                        options={{
                          style: {
                            base: {
                              fontSize: "16px",
                              color: "#ffffff",
                              "::placeholder": {
                                color: "#aab7c4",
                              },
                            },
                            invalid: {
                              color: "#dc3545",
                            },
                          },
                        }}
                      />
                    </div>
                  </>
                )}

                {paymentMethod === "check" && (
                  <div className="mt-4">
                    <p className="text-white">
                      You have selected to pay by check. Please make your check
                      payable to the company.
                    </p>
                  </div>
                )}
              </div>

              <div className="mb-4">
                <label className="block mb-2 text-white">
                  Payment Notes (Optional)
                </label>
                <textarea
                  value={paymentNotes}
                  onChange={(e) => setPaymentNotes(e.target.value)}
                  className="w-full rounded border-[1.5px] border-stroke bg-transparent px-5 py-3 text-white outline-none transition focus:border-primary active:border-primary"
                  placeholder="Add any notes about this payment"
                  rows={2}
                />
              </div>
            </div>
          </div>

          {errorMessage && (
            <div className="p-3 mb-4 text-sm rounded bg-danger/10 text-danger">
              {errorMessage}
            </div>
          )}

          <div className="flex justify-end mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 mr-2 text-sm font-medium text-white rounded bg-meta-4 hover:bg-opacity-90"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!stripe || isProcessing || remainingAmount <= 0}
              className="px-4 py-2 text-sm font-medium text-white rounded bg-success hover:bg-opacity-90 disabled:bg-opacity-50"
            >
              {isProcessing
                ? "Processing..."
                : `Pay $${remainingAmount.toFixed(2)}`}
            </button>
          </div>
        </form>
      </div>
    </CustomModal>
  );
};

const ClientViewInvoicePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [loading, setLoading] = useState(true);
  const [invoice, setInvoice] = useState(null);
  const [userDetails, setUserDetails] = useState(null);
  const [companyInfo, setCompanyInfo] = useState(null);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedInvoiceItemId, setSelectedInvoiceItemId] = useState(null);
  const [isPayAllModalOpen, setIsPayAllModalOpen] = useState(false);
  const [mixSeasons, setMixSeasons] = useState({});
  const [producerDetails, setProducerDetails] = useState({});

  const fetchCompanyInfo = async () => {
    try {
      const sdk = new MkdSDK();
      const result = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/company/info",
        {},
        "GET"
      );
      if (!result.error) {
        setCompanyInfo(result);
      }
    } catch (error) {
      console.error("Error fetching company info:", error);
    }
  };

  const fetchMixSeasonsForProducer = async (producerId) => {
    if (!producerId) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v4/api/records/mix_season?filter=user_id,eq,${producerId}`,
        {},
        "GET"
      );

      if (!response.error && response.list) {
        // Sort seasons in ascending order
        const sortedSeasons = response.list.sort((a, b) => {
          // First try to sort by name if it contains numbers
          const aNum = parseInt(a.name.replace(/\D/g, ""));
          const bNum = parseInt(b.name.replace(/\D/g, ""));

          if (!isNaN(aNum) && !isNaN(bNum)) {
            return aNum - bNum;
          }

          // Fall back to alphabetical sort
          return a.name.localeCompare(b.name);
        });

        // Update the mixSeasons state with the new data for this producer
        setMixSeasons((prev) => ({
          ...prev,
          [producerId]: sortedSeasons,
        }));
      }
    } catch (error) {
      console.error(
        `Error fetching mix seasons for producer ${producerId}:`,
        error
      );
    }
  };

  const fetchProducerDetails = async (producerId) => {
    if (!producerId) return;

    try {
      const sdk = new MkdSDK();
      const response = await getUserDetailsByIdAPI(producerId);

      if (response) {
        // Update the producerDetails state with the new data for this producer
        setProducerDetails((prev) => ({
          ...prev,
          [producerId]: response,
        }));
      }
    } catch (error) {
      console.error(
        `Error fetching details for producer ${producerId}:`,
        error
      );
    }
  };

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: { path: "invoices" },
    });

    const fetchInvoiceDetails = async () => {
      try {
        setLoading(true);
        const sdk = new MkdSDK();

        // Use the direct API endpoint instead of sdk.getInvoiceById
        const result = await sdk.callRawAPI(
          `/v3/api/custom/equality_record/subscription/invoice/${id}`,
          {},
          "GET"
        );

        if (result.error) {
          showToast(globalDispatch, "Failed to fetch invoice details", "error");
          navigate("/client/invoices");
          return;
        }

        setInvoice(result);

        // Calculate total price is already in the API response
        // No need to calculate it manually

        // Fetch company info to map producer IDs to names
        await fetchCompanyInfo();

        if (result?.invoice?.user_id) {
          const userResult = await getUserDetailsByIdAPI(
            result.invoice.user_id
          );
          setUserDetails(userResult);
        }

        // Fetch mix seasons and producer details for each invoice item
        if (result.items && result.items.length > 0) {
          const producerIds = new Set();

          // Collect all unique producer IDs
          result.items.forEach((item) => {
            if (item.producer) {
              producerIds.add(item.producer);
            }
          });

          // Fetch mix seasons and producer details for each unique producer
          const promises = [];
          producerIds.forEach((producerId) => {
            promises.push(fetchMixSeasonsForProducer(producerId));
            promises.push(fetchProducerDetails(producerId));
          });

          await Promise.all(promises);
        }
      } catch (error) {
        console.error("Error fetching invoice:", error);
        tokenExpireError(dispatch, error.message);
        showToast(globalDispatch, "Failed to fetch invoice details", "error");
      } finally {
        setLoading(false);
      }
    };

    fetchInvoiceDetails();
  }, [id, globalDispatch, dispatch, navigate]);

  console.log(producerDetails);

  const getProducerName = (producerId) => {
    // First check if we have the producer details in our state
    if (producerDetails[producerId]) {
      return `${producerDetails[producerId].model.first_name} ${producerDetails[producerId].model.last_name}`;
    }

    // Fall back to company info if available
    if (companyInfo && companyInfo.members) {
      const producer = companyInfo.members.find(
        (member) => member.id === parseInt(producerId)
      );
      if (producer) {
        return producer.name;
      }
    }

    // If we have a string producer name in the item, use that
    if (typeof producerId === "string" && !parseInt(producerId)) {
      return producerId;
    }

    return "Unknown";
  };

  const getMixSeasonName = (mixSeasonId, producerId) => {
    if (!mixSeasonId || !producerId || !mixSeasons[producerId]) {
      return "Unknown";
    }

    const season = mixSeasons[producerId].find(
      (season) => season.id === parseInt(mixSeasonId)
    );

    return season ? season.name : "Unknown";
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <ClipLoader color="#fff" size={30} />
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-danger">Invoice Not Found</h2>
        </div>
      </div>
    );
  }

  // Function to refresh invoice data after payment
  const handlePaymentComplete = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();

      const result = await sdk.callRawAPI(
        `/v3/api/custom/equality_record/subscription/invoice/${id}`,
        {},
        "GET"
      );

      if (!result.error) {
        setInvoice(result);

        // Refresh mix seasons and producer details for each invoice item
        if (result.items && result.items.length > 0) {
          const producerIds = new Set();

          // Collect all unique producer IDs
          result.items.forEach((item) => {
            if (item.producer) {
              producerIds.add(item.producer);
            }
          });

          // Fetch mix seasons and producer details for each unique producer
          const promises = [];
          producerIds.forEach((producerId) => {
            promises.push(fetchMixSeasonsForProducer(producerId));
            promises.push(fetchProducerDetails(producerId));
          });

          await Promise.all(promises);
        }
      }
    } catch (error) {
      console.error("Error refreshing invoice data:", error);
      showToast(globalDispatch, "Failed to refresh invoice data", "error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 rounded-lg bg-boxdark">
      {/* Company Information Header */}
      <div className="flex justify-between items-center p-4 mb-6 rounded-lg bg-meta-4">
        <div className="flex gap-4 items-center">
          {userDetails?.model?.company_logo ? (
            <img
              src={userDetails.model.company_logo}
              alt={userDetails?.model?.company_name || "Company Logo"}
              className="object-contain w-auto h-16"
            />
          ) : null}
          <div>
            <h2 className="text-xl font-bold text-white">
              {userDetails?.model?.company_name || "Your Company"}
            </h2>
            <p className="text-sm text-bodydark2">
              {userDetails?.model?.office_email || ""}
            </p>
          </div>
        </div>
        <div className="text-right">
          <h2 className="text-2xl font-bold text-white">
            {invoice.invoice?.status === "quote" ? "QUOTE" : "INVOICE"}
          </h2>
        </div>
      </div>

      {/* Header with back button */}
      <div className="flex justify-between items-center mb-6">
        <button
          onClick={() => navigate("/client/invoices")}
          className="flex gap-2 items-center text-white hover:text-primary"
        >
          <ArrowLeft className="w-5 h-5" />
          Back to Invoices
        </button>
        <h2 className="text-2xl font-bold text-white">
          {invoice.invoice?.status === "quote" ? "Quote" : "Invoice"} #
          {invoice.invoice.id}
        </h2>
        <button
          onClick={() => handlePaymentComplete()}
          className="flex gap-2 items-center text-white hover:text-primary"
        >
          <RefreshCw className="w-5 h-5" />
          Refresh
        </button>
      </div>

      {/* Client Information */}
      <div className="pb-2 mb-3">
        <h3 className="mb-2 text-base font-semibold text-white">
          Client Information
        </h3>
        <div className="grid grid-cols-2 gap-6 p-4 rounded-lg bg-meta-4/20">
          <div>
            <p className="text-sm text-bodydark2">Name</p>
            <p className="mt-1 font-medium">{invoice.invoice.client_name}</p>
          </div>
          <div>
            <p className="text-sm text-bodydark2">Email</p>
            <p className="mt-1 font-medium">{invoice.invoice.client_email}</p>
          </div>
          <div>
            <p className="text-sm text-bodydark2">Program</p>
            <p className="mt-1 font-medium">{invoice.invoice.program}</p>
          </div>
          <div>
            <p className="text-sm text-bodydark2">Invoice Date</p>
            <p className="mt-1 font-medium">
              {moment(invoice.invoice.invoice_date).format("MMM DD, YYYY")}
            </p>
          </div>
        </div>
      </div>

      {/* Invoice Items */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-base font-semibold text-white">Invoice Items</h3>
        </div>
        <div className="custom-overflow min-h-[140px] overflow-x-auto">
          <table className="relative w-full table-auto">
            <thead className="bg-meta-4">
              <tr>
                <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                  Mix Date
                </th>
                <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                  Producer
                </th>
                <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                  Description
                </th>
                <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                  Team Name
                </th>
                <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                  Division
                </th>
                <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                  Discount
                </th>
                <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                  Price
                </th>
                <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                  Amount Left
                </th>
                <th className="px-4 py-3 text-xs font-medium tracking-wider text-center text-white uppercase">
                  Status
                </th>
                <th className="px-4 py-3 text-xs font-medium tracking-wider text-center text-white uppercase">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="text-white">
              {invoice.items?.map((item) => (
                <tr
                  key={item.id}
                  className={`border-b border-stroke/50 text-[12px] hover:bg-primary/5 ${
                    item.is_special === 1 ? "bg-meta-4/20" : ""
                  }`}
                >
                  {item.is_special === 1 ? (
                    // Special row (charge)
                    <>
                      <td colSpan={3} className="px-4 py-3 whitespace-nowrap">
                        {item.description || "Additional Charge"}
                      </td>
                      <td colSpan={2} className="px-4 py-3 whitespace-nowrap">
                        {/* Empty cells for team name, division */}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {parseFloat(item.discount) > 0
                          ? `${parseFloat(item.discount).toFixed(2)}%`
                          : "0%"}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        ${parseFloat(item.total || 0).toFixed(2)}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        ${(parseFloat(item.amount_left || 0) / 100).toFixed(2)}
                      </td>
                      <td className="px-4 py-3 text-center whitespace-nowrap">
                        {item.status === "2" || item.status === 2 ? (
                          <span className="px-2 py-1 text-xs font-medium rounded bg-primary/10 text-primary">
                            Deposit Paid
                          </span>
                        ) : item.status === "1" || item.status === 1 ? (
                          <span className="px-2 py-1 text-xs font-medium rounded bg-success/10 text-success">
                            Complete
                          </span>
                        ) : item.status === "3" || item.status === 3 ? (
                          <span className="px-2 py-1 text-xs font-medium rounded bg-success/10 text-success">
                            Paid in Full
                          </span>
                        ) : item.status === "4" || item.status === 4 ? (
                          <span className="px-2 py-1 text-xs font-medium rounded bg-warning/10 text-warning">
                            Awaiting Edit
                          </span>
                        ) : (
                          <span className="px-2 py-1 text-xs font-medium rounded bg-warning/10 text-warning">
                            Unpaid
                          </span>
                        )}
                      </td>
                      <td className="px-4 py-3 text-center whitespace-nowrap">
                        {parseFloat(item.amount_left) > 0 ||
                        item.status === "2" ||
                        item.status === 2 ? (
                          <button
                            onClick={() => {
                              setSelectedInvoiceItemId(item.id);
                              setIsPaymentModalOpen(true);
                            }}
                            className="flex gap-1 items-center px-2 py-1 text-xs font-medium text-white rounded bg-primary hover:bg-opacity-90"
                          >
                            <CreditCardIcon className="w-3 h-3" />
                            {item.status === "2" || item.status === 2
                              ? "Pay Remaining"
                              : "Pay Now"}
                          </button>
                        ) : (
                          <span className="text-xs text-bodydark2">-</span>
                        )}
                      </td>
                    </>
                  ) : (
                    // Regular row
                    <>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {item.mix_date
                          ? moment(item.mix_date).format("MM/DD/YYYY")
                          : ""}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {item.producer && getProducerName(item.producer)}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div>
                          <p>{item.description || "Mix Package"}</p>
                          {item.mix_season_id && (
                            <p className="text-xs text-bodydark2">
                              Season:{" "}
                              {getMixSeasonName(
                                item.mix_season_id,
                                item.producer
                              )}
                            </p>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {item.team_name || ""}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {item.division || ""}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {parseFloat(item.discount) > 0
                          ? `${parseFloat(item.discount).toFixed(2)}%`
                          : "0%"}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        ${parseFloat(item.total || 0).toFixed(2)}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        ${(parseFloat(item.amount_left || 0) / 100).toFixed(2)}
                      </td>
                      <td className="px-4 py-3 text-center whitespace-nowrap">
                        {item.status === "2" || item.status === 2 ? (
                          <span className="px-2 py-1 text-xs font-medium rounded bg-primary/10 text-primary">
                            Deposit Paid
                          </span>
                        ) : item.status === "1" || item.status === 1 ? (
                          <span className="px-2 py-1 text-xs font-medium rounded bg-success/10 text-success">
                            Complete
                          </span>
                        ) : item.status === "3" || item.status === 3 ? (
                          <span className="px-2 py-1 text-xs font-medium rounded bg-success/10 text-success">
                            Paid in Full
                          </span>
                        ) : item.status === "4" || item.status === 4 ? (
                          <span className="px-2 py-1 text-xs font-medium rounded bg-warning/10 text-warning">
                            Awaiting Edit
                          </span>
                        ) : (
                          <span className="px-2 py-1 text-xs font-medium rounded bg-warning/10 text-warning">
                            Unpaid
                          </span>
                        )}
                      </td>
                      <td className="px-4 py-3 text-center whitespace-nowrap">
                        {parseFloat(item.amount_left) > 0 ||
                        item.status === "2" ||
                        item.status === 2 ? (
                          <button
                            onClick={() => {
                              setSelectedInvoiceItemId(item.id);
                              setIsPaymentModalOpen(true);
                            }}
                            className="flex gap-1 items-center px-2 py-1 text-xs font-medium text-white rounded bg-primary hover:bg-opacity-90"
                          >
                            <CreditCardIcon className="w-3 h-3" />
                            {item.status === "2" || item.status === 2
                              ? "Pay Remaining"
                              : "Pay Now"}
                          </button>
                        ) : (
                          <span className="text-xs text-bodydark2">-</span>
                        )}
                      </td>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Invoice Total Section */}
      <div className="p-4 mb-6 rounded-lg bg-meta-4/20">
        <div className="flex justify-end">
          <div className="w-1/3">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-white">
                Invoice total:
              </span>
              <span className="text-lg font-bold text-white">
                ${parseFloat(invoice.invoice.total || 0).toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-white">
                Amount paid:
              </span>
              <span className="text-sm text-white">
                $
                {(
                  parseFloat(invoice.invoice.payment_amount || 0) / 100
                ).toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between items-center pt-2 border-t border-stroke/50">
              <span className="text-sm font-medium text-white">
                Balance due:
              </span>
              <span className="text-sm font-bold text-white">
                $
                {(
                  parseFloat(invoice.invoice.total || 0) -
                  parseFloat(invoice.invoice.payment_amount || 0)
                ).toFixed(2)}
              </span>
            </div>
            {/* Pay All Button */}
            {parseFloat(invoice.invoice.total || 0) >
              parseFloat(invoice.invoice.payment_amount || 0) && (
              <div className="flex justify-end mt-4">
                <button
                  onClick={() => setIsPayAllModalOpen(true)}
                  className="flex gap-2 items-center px-4 py-2 text-sm font-medium text-white rounded bg-success hover:bg-opacity-90"
                >
                  <CreditCardIcon className="w-4 h-4" />
                  Pay All ($
                  {(
                    parseFloat(invoice.invoice.total || 0) -
                    parseFloat(invoice.invoice.payment_amount || 0)
                  ).toFixed(2)}
                  )
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Notes */}
      {invoice.invoice.notes && (
        <div className="mb-6">
          <h3 className="mb-2 text-base font-semibold text-white">Notes</h3>
          <div className="p-4 rounded-lg bg-meta-4/20">
            <p className="whitespace-pre-wrap text-bodydark">
              {invoice.invoice.notes}
            </p>
          </div>
        </div>
      )}

      {/* Terms and Conditions */}
      {invoice.invoice.terms_and_conditions && (
        <div className="mb-6">
          <h3 className="mb-2 text-base font-semibold text-white">
            Terms and Conditions
          </h3>
          <div className="p-4 rounded-lg bg-meta-4/20">
            <div
              className="text-bodydark"
              dangerouslySetInnerHTML={{
                __html: invoice.invoice.terms_and_conditions,
              }}
            />
          </div>
        </div>
      )}

      {/* Service Agreement Section */}
      <div className="p-4 mb-6 rounded-lg bg-meta-4/20">
        <h3 className="mb-2 text-base font-semibold text-white">
          Service Agreement
        </h3>
        {invoice.invoice.attachment_url ? (
          <div className="flex gap-4 items-center">
            <p className="text-bodydark2">
              Service agreement has been signed and is available for download.
            </p>
            <a
              href={invoice.invoice.attachment_url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex gap-2 items-center px-4 py-2 text-sm font-medium text-white rounded bg-primary hover:bg-opacity-90"
            >
              <Download className="w-4 h-4" />
              Download Service Agreement PDF
            </a>
          </div>
        ) : (
          <p className="text-bodydark2">
            Service agreement is not available yet.
          </p>
        )}
      </div>

      {/* Check Uploads Section */}
      {invoice.invoice.checks &&
        (() => {
          try {
            const checks = JSON.parse(invoice.invoice.checks);
            const validChecks = checks.filter(
              (check) => check.url && check.id && check.uploadDate
            );
            return (
              validChecks.length > 0 && (
                <div className="p-4 mb-6 rounded-lg bg-meta-4/20">
                  <h3 className="mb-4 text-base font-semibold text-white">
                    Check Uploads
                  </h3>
                  <div className="space-y-2">
                    {validChecks.map((check) => (
                      <div
                        key={check.id}
                        className="flex justify-between items-center p-3 rounded border border-stroke/50 bg-boxdark-2/40"
                      >
                        <div className="flex gap-3 items-center">
                          <FileText className="w-4 h-4 text-primary" />
                          <div>
                            <p className="text-sm font-medium text-white">
                              {check.filename || "Unknown File"}
                            </p>
                            <p className="text-xs text-bodydark2">
                              Uploaded:{" "}
                              {moment(check.uploadDate).format(
                                "MMM DD, YYYY HH:mm"
                              )}
                            </p>
                          </div>
                        </div>
                        <div className="flex gap-2 items-center">
                          <a
                            href={check.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex gap-1 items-center px-3 py-1 text-xs font-medium text-white rounded bg-meta-5 hover:bg-opacity-90"
                          >
                            <FileText className="w-3 h-3" />
                            View
                          </a>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            );
          } catch (error) {
            console.error("Error parsing checks data:", error);
            return null;
          }
        })()}

      {/* Payment History */}
      {invoice.payments && invoice.payments.length > 0 && (
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-base font-semibold text-white">
              Payment History
            </h3>
          </div>
          <div className="overflow-x-auto custom-overflow">
            <table className="relative w-full table-auto">
              <thead className="bg-meta-4">
                <tr>
                  <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                    Date
                  </th>
                  <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                    Method
                  </th>
                  <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                    Status
                  </th>
                  <th className="px-4 py-3 text-xs font-medium tracking-wider text-right text-white uppercase">
                    Amount
                  </th>
                </tr>
              </thead>
              <tbody className="text-white">
                {invoice.payments.length > 0 ? (
                  invoice.payments.map((payment, index) => (
                    <tr
                      key={index}
                      className="border-b border-stroke/50 text-[12px] hover:bg-primary/5"
                    >
                      <td className="px-4 py-3 whitespace-nowrap">
                        {moment(payment.create_at).format("MM/DD/YYYY")}
                      </td>
                      <td className="px-4 py-3 capitalize whitespace-nowrap">
                        {payment.payment_method || "stripe"}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <span
                          className={`rounded px-2 py-1 text-xs font-medium ${
                            payment.status === "succeeded"
                              ? "bg-success/10 text-success"
                              : "bg-warning/10 text-warning"
                          }`}
                        >
                          {payment.status || "pending"}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-right whitespace-nowrap">
                        ${parseFloat(payment.amount / 100).toFixed(2)}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={4}
                      className="px-4 py-3 text-center text-bodydark2"
                    >
                      No payment records found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Payment Modal */}
      {isPaymentModalOpen && (
        <Elements stripe={stripePromise}>
          <PaymentModal
            isOpen={isPaymentModalOpen}
            onClose={() => setIsPaymentModalOpen(false)}
            invoiceItemId={selectedInvoiceItemId}
            onPaymentComplete={handlePaymentComplete}
          />
        </Elements>
      )}

      {/* Pay All Modal */}
      {isPayAllModalOpen && (
        <Elements stripe={stripePromise}>
          <PayAllModal
            isOpen={isPayAllModalOpen}
            onClose={() => setIsPayAllModalOpen(false)}
            invoice={invoice}
            onPaymentComplete={handlePaymentComplete}
          />
        </Elements>
      )}
    </div>
  );
};

export default ClientViewInvoicePage;
